// 访客核销页面
const VisitorManager = require('../../../../utils/visitor-manager');
const NotificationManager = require('../../../../utils/notification-manager');
const util = require('@/utils/util.js')
const visitorsApi = require('../../../../api/visitorsApi.js')

Page({
  data: {
    activeTab: 'scan', // 当前激活的标签页：scan-扫码核销，search-搜索核销
    searchKeyword: '', // 搜索关键词
    searchResults: [], // 搜索结果
    showVerifyModal: false, // 是否显示核验弹窗
    currentVisitor: null, // 当前选中的访客
    isLoading: false, // 是否正在加载

    // 分页相关
    pageNum: 1,
    pageSize: 10,
    total: 0,
    hasMore: true,
    loadingMore: false,

    // 字典数据
    visitorStatusDict: [] // 访客状态字典
  },

  onLoad: function() {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '访客核销'
    });

    // 初始化字典数据
    this.initDictData();
  },

  // 初始化字典数据
  initDictData: function() {
    try {
      // 获取访客状态字典
      const visitorStatusDict = util.getDictByNameEn('visitor_status')[0].children || [];

      this.setData({
        visitorStatusDict: visitorStatusDict
      });

      console.log('字典数据加载完成:', {
        visitorStatusDict: visitorStatusDict.length
      });
    } catch (error) {
      console.error('字典数据加载失败:', error);
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      });
    }
  },

  onShow: function() {
    // 如果当前是搜索标签页，刷新搜索结果
    if (this.data.activeTab === 'search' && this.data.searchKeyword) {
      this.loadVisitorList(true);
    }
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    if (this.data.activeTab === 'search') {
      this.loadVisitorList(true);
    } else {
      wx.stopPullDownRefresh();
    }
  },

  // 上拉加载更多
  onReachBottom: function() {
    if (this.data.activeTab === 'search' && this.data.hasMore && !this.data.loadingMore) {
      this.loadVisitorList(false);
    }
  },

  // 切换标签页
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab
    });
    
    if(tab === 'search')
    {
      this.loadVisitorList()
    }
  },

  // 扫码核销
  scanCode: function() {
    // wx.scanCode({
    //   onlyFromCamera: true,
    //   scanType: ['qrCode'],
    //   success: (res) => {
    //     try {
    //       // 解析二维码内容，获取访客ID
    //       const qrData = JSON.parse(res.result);
    //       if (qrData && qrData.visitorId) {
    //         // 获取访客信息
    //         this.getVisitorById(qrData.visitorId);
    //       } else {
    //         wx.showToast({
    //           title: '无效的访客码',
    //           icon: 'none'
    //         });
    //       }
    //     } catch (error) {
    //       console.error('解析二维码失败', error);
    //       wx.showToast({
    //         title: '无效的访客码',
    //         icon: 'none'
    //       });
    //     }
    //   },
    //   fail: (err) => {
    //     console.error('扫码失败', err);
    //     if (err.errMsg !== 'scanCode:fail cancel') {
    //       wx.showToast({
    //         title: '扫码失败，请重试',
    //         icon: 'none'
    //       });
    //     }
    //   }
    // });


    util.checkStatusAndNavigate('/pages/qrCodeScan/qrCodeScan', {
      requireAuth: true,
      requireCommunity: true
    });


  },

  // 根据ID获取访客信息
  getVisitorById: function(id) {
    

     // 跳转到访客凭证页面，添加扫码标识
     wx.navigateTo({
      url: `/servicePackage/pages/visitor/credential/index?id=${id}&fromScan=true`
    });

  },

  // 加载访客列表
  loadVisitorList: function(refresh = false) {
    debugger
    const keyword = this.data.searchKeyword.trim();

 
      this.setData({
        searchResults: [],
        total: 0,
        hasMore: false
      });
  
    

    // 如果是刷新，重置分页
    if (refresh) {
      this.setData({
        pageNum: 1,
        searchResults: [],
        hasMore: true
      });
    }

    // 防止重复加载
    if (this.data.loadingMore && !refresh) {
      return;
    }

    this.setData({
      isLoading: refresh,
      loadingMore: !refresh
    });

    // 智能判断输入类型
    const searchParams = this.parseSearchKeyword(keyword);

    const params = {
      pageNum: refresh ? 1 : this.data.pageNum,
      pageSize: this.data.pageSize,
      communityId: wx.getStorageSync('selectedCommunity').id,
      ...searchParams
    };

    console.log('访客列表请求参数:', params);

    visitorsApi.getPropertyVisitorList(params)
      .then(res => {
        console.log('访客列表响应:', res);

        if (res && res.list) {
          // 为每个访客项添加状态文本
          const processedList = res.list.map(item => ({
            ...item,
            statusText: this.formatVisitorStatus(item.status)
          }));

          const newList = refresh ? processedList : [...this.data.searchResults, ...processedList];
          const hasMore = res.pageNum < res.pages;

          this.setData({
            searchResults: newList,
            total: res.total,
            pageNum: res.pageNum + 1,
            hasMore: hasMore,
            isLoading: false,
            loadingMore: false
          });

          // 停止下拉刷新
          if (refresh) {
            wx.stopPullDownRefresh();
          }
        } else {
          throw new Error('数据格式错误');
        }
      })
      .catch(err => {
        console.error('获取访客列表失败:', err);
        wx.showToast({
          title: '获取访客列表失败',
          icon: 'none'
        });

        this.setData({
          isLoading: false,
          loadingMore: false
        });

        if (refresh) {
          wx.stopPullDownRefresh();
        }
      });
  },

  // 智能解析搜索关键词
  parseSearchKeyword: function(keyword) {
    // 判断是否为纯数字（手机号）
    const isPhone = /^\d+$/.test(keyword);

    if (isPhone) {
      // 纯数字，作为手机号搜索
      return { phone: keyword };
    } else {
      // 包含字符，作为姓名搜索
      return { visitorName: keyword };
    }
  },

  // 输入搜索关键词
  inputKeyword: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 清空搜索关键词
  clearKeyword: function() {
    this.setData({
      searchKeyword: '',
      searchResults: [],
      total: 0,
      hasMore: false,
      pageNum: 1
    });
  },

  // 点击搜索按钮
  handleSearch: function() {
    this.loadVisitorList(true);
  },

  // 格式化访客状态
  formatVisitorStatus: function(status) {
    const statusDict = this.data.visitorStatusDict;
    const statusItem = statusDict.find(item => item.nameEn === status);
    return statusItem ? statusItem.nameCn : status;
  },

  // 格式化时间显示
  formatDateTime: function(dateTimeStr) {
    if (!dateTimeStr) return '';

    try {
      const date = new Date(dateTimeStr);
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');

      return `${month}-${day} ${hours}:${minutes}`;
    } catch (error) {
      console.error('时间格式化失败:', error);
      return dateTimeStr;
    }
  },

  // 从搜索结果中选择访客
  selectVisitor: function(e) {
    const id = e.currentTarget.dataset.id;
    this.getVisitorById(id);
  },

  // 隐藏核验弹窗
  hideVerifyModal: function() {
    this.setData({
      showVerifyModal: false,
      currentVisitor: null
    });
  },

  // 核验访客
  verifyVisitor: function() {
    const { currentVisitor } = this.data;
    
    if (!currentVisitor) {
      wx.showToast({
        title: '访客信息不完整',
        icon: 'none'
      });
      return;
    }

    // 检查访客状态
    if (currentVisitor.status !== 'pending') {
      wx.showToast({
        title: '此凭证已不可用',
        icon: 'none'
      });
      this.hideVerifyModal();
      return;
    }
    
    // 更新访客状态为已到访
    const updated = VisitorManager.updateVisitorStatus(currentVisitor.id, 'visited');
    
    if (updated) {
      // 发送访客到达通知
      this.sendVisitorArrivedNotification(currentVisitor);
      
      wx.showToast({
        title: '核销成功',
        icon: 'success'
      });
      
      // 如果在搜索页面，刷新搜索结果
      if (this.data.activeTab === 'search' && this.data.searchKeyword) {
        this.searchVisitors();
      }
    } else {
      wx.showToast({
        title: '核销失败，请重试',
        icon: 'none'
      });
    }
    
    this.hideVerifyModal();
  },

  // 发送访客到达通知
  sendVisitorArrivedNotification: function(visitorData) {
    // 请求访客到达通知
    NotificationManager.requestVisitorArrivedSubscription()
      .then(res => {
        console.log('访客到达通知订阅结果:', res);
        
        // 如果用户接受了订阅，则发送通知
        if (res[NotificationManager.templates.visitorArrived] === 'accept') {
          // 发送访客到达通知
          NotificationManager.sendVisitorArrivedNotification(visitorData)
            .then(result => {
              console.log('发送访客到达通知结果:', result);
            })
            .catch(err => {
              console.error('发送访客到达通知失败:', err);
            });
        }
      })
      .catch(err => {
        console.error('请求访客到达通知订阅失败:', err);
      });
  }
});
