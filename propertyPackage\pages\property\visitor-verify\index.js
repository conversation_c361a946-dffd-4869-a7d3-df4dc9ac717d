// 访客核销页面
const VisitorManager = require('../../../../utils/visitor-manager');
const NotificationManager = require('../../../../utils/notification-manager');
const util = require('@/utils/util.js')
const visitorsApi = require('../../../../api/visitorsApi.js')

Page({
  data: {
    activeTab: 'scan', // 当前激活的标签页：scan-扫码核销，search-搜索核销
    searchKeyword: '', // 搜索关键词
    searchResults: [], // 搜索结果
    showVerifyModal: false, // 是否显示核验弹窗
    currentVisitor: null, // 当前选中的访客
    isLoading: false, // 是否正在加载

    // 分页相关
    pageNum: 1,
    pageSize: 10,
    total: 0,
    hasMore: true,
    loadingMore: false,

    // 字典数据
    visitorStatusDict: [] // 访客状态字典
  },

  onLoad: function() {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '访客核销'
    });

    // 初始化字典数据
    this.initDictData();
  },

  // 初始化字典数据
  initDictData: function() {
    try {
      // 获取访客状态字典
      const visitorStatusDict = util.getDictByNameEn('visitor_status')[0].children || [];

      this.setData({
        visitorStatusDict: visitorStatusDict
      });

      console.log('字典数据加载完成:', {
        visitorStatusDict: visitorStatusDict.length
      });
    } catch (error) {
      console.error('字典数据加载失败:', error);
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      });
    }
  },

  onShow: function() {
    // 如果当前是搜索标签页，刷新搜索结果
    if (this.data.activeTab === 'search' && this.data.searchKeyword) {
      this.loadVisitorList(true);
    }
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    if (this.data.activeTab === 'search') {
      this.loadVisitorList(true);
    } else {
      wx.stopPullDownRefresh();
    }
  },

  // 上拉加载更多
  onReachBottom: function() {
    if (this.data.activeTab === 'search' && this.data.hasMore && !this.data.loadingMore) {
      this.loadVisitorList(false);
    }
  },

  // 切换标签页
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab
    });
  },

  // 扫码核销
  scanCode: function() {
    // wx.scanCode({
    //   onlyFromCamera: true,
    //   scanType: ['qrCode'],
    //   success: (res) => {
    //     try {
    //       // 解析二维码内容，获取访客ID
    //       const qrData = JSON.parse(res.result);
    //       if (qrData && qrData.visitorId) {
    //         // 获取访客信息
    //         this.getVisitorById(qrData.visitorId);
    //       } else {
    //         wx.showToast({
    //           title: '无效的访客码',
    //           icon: 'none'
    //         });
    //       }
    //     } catch (error) {
    //       console.error('解析二维码失败', error);
    //       wx.showToast({
    //         title: '无效的访客码',
    //         icon: 'none'
    //       });
    //     }
    //   },
    //   fail: (err) => {
    //     console.error('扫码失败', err);
    //     if (err.errMsg !== 'scanCode:fail cancel') {
    //       wx.showToast({
    //         title: '扫码失败，请重试',
    //         icon: 'none'
    //       });
    //     }
    //   }
    // });


    util.checkStatusAndNavigate('/pages/qrCodeScan/qrCodeScan', {
      requireAuth: true,
      requireCommunity: true
    });


  },

  // 根据ID获取访客信息
  getVisitorById: function(id) {
    this.setData({ isLoading: true });

    // 获取访客数据
    const visitorData = VisitorManager.getVisitorById(id);
    
    if (!visitorData) {
      wx.showToast({
        title: '未找到访客信息',
        icon: 'none'
      });
      this.setData({ isLoading: false });
      return;
    }

    // 解密敏感信息
    const decryptedVisitor = VisitorManager.decryptVisitorData(visitorData);
    
    this.setData({
      currentVisitor: decryptedVisitor,
      showVerifyModal: true,
      isLoading: false
    });
  },

  // 搜索访客
  searchVisitors: function() {
    const keyword = this.data.searchKeyword.trim();
    
    if (!keyword) {
      this.setData({
        searchResults: []
      });
      return;
    }

    this.setData({ isLoading: true });

    // 使用VisitorManager搜索访客
    const visitors = VisitorManager.filterVisitors('pending', keyword);
    
    this.setData({
      searchResults: visitors,
      isLoading: false
    });
  },

  // 输入搜索关键词
  inputKeyword: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 清空搜索关键词
  clearKeyword: function() {
    this.setData({
      searchKeyword: '',
      searchResults: []
    });
  },

  // 点击搜索按钮
  handleSearch: function() {
    this.searchVisitors();
  },

  // 从搜索结果中选择访客
  selectVisitor: function(e) {
    const id = e.currentTarget.dataset.id;
    this.getVisitorById(id);
  },

  // 隐藏核验弹窗
  hideVerifyModal: function() {
    this.setData({
      showVerifyModal: false,
      currentVisitor: null
    });
  },

  // 核验访客
  verifyVisitor: function() {
    const { currentVisitor } = this.data;
    
    if (!currentVisitor) {
      wx.showToast({
        title: '访客信息不完整',
        icon: 'none'
      });
      return;
    }

    // 检查访客状态
    if (currentVisitor.status !== 'pending') {
      wx.showToast({
        title: '此凭证已不可用',
        icon: 'none'
      });
      this.hideVerifyModal();
      return;
    }
    
    // 更新访客状态为已到访
    const updated = VisitorManager.updateVisitorStatus(currentVisitor.id, 'visited');
    
    if (updated) {
      // 发送访客到达通知
      this.sendVisitorArrivedNotification(currentVisitor);
      
      wx.showToast({
        title: '核销成功',
        icon: 'success'
      });
      
      // 如果在搜索页面，刷新搜索结果
      if (this.data.activeTab === 'search' && this.data.searchKeyword) {
        this.searchVisitors();
      }
    } else {
      wx.showToast({
        title: '核销失败，请重试',
        icon: 'none'
      });
    }
    
    this.hideVerifyModal();
  },

  // 发送访客到达通知
  sendVisitorArrivedNotification: function(visitorData) {
    // 请求访客到达通知
    NotificationManager.requestVisitorArrivedSubscription()
      .then(res => {
        console.log('访客到达通知订阅结果:', res);
        
        // 如果用户接受了订阅，则发送通知
        if (res[NotificationManager.templates.visitorArrived] === 'accept') {
          // 发送访客到达通知
          NotificationManager.sendVisitorArrivedNotification(visitorData)
            .then(result => {
              console.log('发送访客到达通知结果:', result);
            })
            .catch(err => {
              console.error('发送访客到达通知失败:', err);
            });
        }
      })
      .catch(err => {
        console.error('请求访客到达通知订阅失败:', err);
      });
  }
});
