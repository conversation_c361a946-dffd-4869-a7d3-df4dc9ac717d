<!--添加居民页面-->
<view class="container">
  <view class="form-container">
    <!-- 基本信息 -->
    <view class="form-section">
      <view class="section-title">基本信息</view>

      <!-- 姓名 -->
      <view class="form-item {{errors.residentName ? 'error' : ''}}">
        <view class="form-label">姓名</view>
        <input class="form-input" type="text" value="{{formData.residentName}}" data-field="residentName" bindinput="onInput" placeholder="请输入姓名" />
        <view class="error-message" wx:if="{{errors.residentName}}">{{errors.residentName}}</view>
      </view>

      <!-- 手机号 -->
      <view class="form-item {{errors.phone ? 'error' : ''}}">
        <view class="form-label">手机号</view>
        <input class="form-input" type="number" value="{{formData.phone}}" data-field="phone" bindinput="onInput" placeholder="请输入手机号" maxlength="11" />
        <view class="error-message" wx:if="{{errors.phone}}">{{errors.phone}}</view>
      </view>

      <!-- 证件类型 -->
      <view class="form-item">
        <view class="form-label">证件类型</view>
        <picker mode="selector" range="{{certificateType}}" range-key="nameCn" bindchange="onCertificateTypeChange">
          <view class="picker-value">
            <block wx:for="{{certificateType}}" wx:key="nameEn">
              <block wx:if="{{item.nameEn === formData.certificateType}}">{{item.nameCn}}</block>
            </block>
          </view>
        </picker>
      </view>

      <!-- 证件号码 -->
      <view class="form-item {{errors.idCardNumber ? 'error' : ''}}">
        <view class="form-label">证件号码</view>
        <input class="form-input" type="idcard" value="{{formData.idCardNumber}}" data-field="idCardNumber" bindinput="onInput" placeholder="请输入证件号码" maxlength="18" />
        <view class="error-message" wx:if="{{errors.idCardNumber}}">{{errors.idCardNumber}}</view>
      </view>

      <!-- 性别 -->
      <view class="form-item">
        <view class="form-label">性别</view>
        <picker mode="selector" range="{{genderDict}}" range-key="nameCn" bindchange="onGenderChange">
          <view class="picker-value">
            <block wx:for="{{genderDict}}" wx:key="nameEn">
              <block wx:if="{{item.nameEn === formData.gender}}">{{item.nameCn}}</block>
            </block>
          </view>
        </picker>
      </view>

      <!-- 出生日期 -->
      <view class="form-item {{errors.birthday ? 'error' : ''}}">
        <view class="form-label">出生日期</view>
        <picker mode="date" value="{{formData.birthday}}" bindchange="onBirthDateChange">
          <view class="picker-value">{{formData.birthday || '请选择出生日期'}}</view>
        </picker>
        <view class="error-message" wx:if="{{errors.birthday}}">{{errors.birthday}}</view>
      </view>

      <!-- 居民类型 -->
      <view class="form-item">
        <view class="form-label">居民类型</view>
        <picker mode="selector" range="{{residentType}}" range-key="nameCn" bindchange="onResidentTypeChange">
          <view class="picker-value">
            <block wx:for="{{residentType}}" wx:key="nameEn">
              <block wx:if="{{item.nameEn === formData.residentType}}">{{item.nameCn}}</block>
            </block>
          </view>
        </picker>
      </view>

      <!-- 人员标签 -->
      <view class="form-item">
        <view class="form-label">人员标签</view>
        <checkbox-group bindchange="onTagChange">
          <view class="checkbox-group">
            <label class="checkbox-item" wx:for="{{residentTag}}" wx:key="nameEn">
              <checkbox value="{{index}}" checked="{{formData.tags.includes(item.nameEn)}}"/>
              <text class="checkbox-text">{{item.nameCn}}</text>
            </label>
          </view>
        </checkbox-group>
      </view>

      <!-- 备注 -->
      <view class="form-item">
        <view class="form-label">备注</view>
        <textarea class="form-textarea" value="{{formData.note}}" data-field="note" bindinput="onInput" placeholder="请输入备注（选填）" maxlength="200"></textarea>
      </view>
    </view>

    <!-- 房屋信息 -->
    <view class="form-section">
      <view class="section-title">房屋信息</view>

      <!-- 关联房屋 -->
      <view class="form-item {{errors.roomId ? 'error' : ''}}">
        <view class="form-label">关联房屋</view>
        <view class="house-selector" bindtap="showHouseSelector">
          <text class="house-value">{{formData.roomName || '请选择关联房屋'}}</text>
          <view class="selector-arrow"></view>
        </view>
        <view class="error-message" wx:if="{{errors.roomId}}">{{errors.roomId}}</view>
      </view>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="bottom-actions">
    <button class="btn-cancel" bindtap="cancelAdd">取消</button>
    <button class="btn-submit" bindtap="submitForm" disabled="{{submitting}}">确认</button>
  </view>

  <!-- 房屋选择器弹窗 -->
  <view class="modal {{showHouseSelector ? 'show' : ''}}" catchtouchmove="preventTouchMove">
    <view class="modal-mask" bindtap="hideHouseSelector"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">选择房屋</text>
        <view class="modal-close" bindtap="hideHouseSelector">×</view>
      </view>
      <view class="modal-body">
        <scroll-view scroll-y="true" class="house-list">
          <view class="house-item" wx:for="{{houses}}" wx:key="id" bindtap="selectHouse" data-id="{{item.id}}" data-fullname="{{item.fullName}}">
            <view class="house-info">
              <text class="house-address">{{item.fullName}}</text>
            </view>
            <view class="item-selected" wx:if="{{formData.roomId == item.id}}">✓</view>
          </view>
        </scroll-view>

        <!-- 空状态 -->
        <view class="empty-state" wx:if="{{houses.length === 0}}">
          <view class="empty-icon">🏠</view>
          <view class="empty-text">暂无可选房屋</view>
          <view class="empty-desc">请联系管理员添加房屋信息</view>
        </view>
      </view>
      <view class="modal-footer">
        <button class="btn-cancel" bindtap="hideHouseSelector">取消</button>
      </view>
    </view>
  </view>
</view>