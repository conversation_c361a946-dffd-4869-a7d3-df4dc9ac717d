<!--添加居民页面-->
<view class="container">
  <view class="form-container">
    <!-- 基本信息 -->
    <view class="form-section">
      <view class="section-title">基本信息</view>

      <!-- 姓名 -->
      <view class="form-item {{errors.residentName ? 'error' : ''}}">
        <view class="form-label">姓名</view>
        <input class="form-input" type="text" value="{{formData.residentName}}" data-field="residentName" bindinput="onInput" placeholder="请输入姓名" />
        <view class="error-message" wx:if="{{errors.residentName}}">{{errors.residentName}}</view>
      </view>

      <!-- 手机号 -->
      <view class="form-item {{errors.phone ? 'error' : ''}}">
        <view class="form-label">手机号</view>
        <input class="form-input" type="number" value="{{formData.phone}}" data-field="phone" bindinput="onInput" placeholder="请输入手机号" maxlength="11" />
        <view class="error-message" wx:if="{{errors.phone}}">{{errors.phone}}</view>
      </view>

      <!-- 证件类型 -->
      <view class="form-item">
        <view class="form-label">证件类型</view>
        <picker mode="selector" range="{{certificateType}}" range-key="nameCn" bindchange="onCertificateTypeChange">
          <view class="picker-value">
            <block wx:for="{{certificateType}}" wx:key="nameEn">
              <block wx:if="{{item.nameEn === formData.certificateType}}">{{item.nameCn}}</block>
            </block>
          </view>
        </picker>
      </view>

      <!-- 证件号码 -->
      <view class="form-item {{errors.idCardNumber ? 'error' : ''}}">
        <view class="form-label">证件号码</view>
        <input class="form-input" type="idcard" value="{{formData.idCardNumber}}" data-field="idCardNumber" bindinput="onInput" placeholder="请输入证件号码" maxlength="18" />
        <view class="error-message" wx:if="{{errors.idCardNumber}}">{{errors.idCardNumber}}</view>
      </view>

      <!-- 性别 -->
      <view class="form-item">
        <view class="form-label">性别</view>
        <picker mode="selector" range="{{genderDict}}" range-key="nameCn" bindchange="onGenderChange">
          <view class="picker-value">
            <block wx:for="{{genderDict}}" wx:key="nameEn">
              <block wx:if="{{item.nameEn === formData.gender}}">{{item.nameCn}}</block>
            </block>
          </view>
        </picker>
      </view>

      <!-- 出生日期 -->
      <view class="form-item {{errors.birthday ? 'error' : ''}}">
        <view class="form-label">出生日期</view>
        <picker mode="date" value="{{formData.birthday}}" bindchange="onBirthDateChange">
          <view class="picker-value">{{formData.birthday || '请选择出生日期'}}</view>
        </picker>
        <view class="error-message" wx:if="{{errors.birthday}}">{{errors.birthday}}</view>
      </view>

      <!-- 居民类型 -->
      <view class="form-item">
        <view class="form-label">居民类型</view>
        <picker mode="selector" range="{{residentType}}" range-key="nameCn" bindchange="onResidentTypeChange">
          <view class="picker-value">
            <block wx:for="{{residentType}}" wx:key="nameEn">
              <block wx:if="{{item.nameEn === formData.residentType}}">{{item.nameCn}}</block>
            </block>
          </view>
        </picker>
      </view>

      <!-- 人员标签 -->
      <view class="form-item">
        <view class="form-label">人员标签</view>
        <view class="tag-selector" bindtap="showTagSelector">
          <view class="tag-value">{{selectedTagsText || '请选择人员标签'}}</view>
          <view class="selector-arrow"></view>
        </view>
      </view>

      <!-- 备注 -->
      <view class="form-item">
        <view class="form-label">备注</view>
        <textarea class="form-textarea" value="{{formData.note}}" data-field="note" bindinput="onInput" placeholder="请输入备注（选填）" maxlength="200"></textarea>
      </view>
    </view>

    <!-- 房屋信息 -->
    <view class="form-section">
      <view class="section-title">房屋信息</view>

      <!-- 关联房屋 -->
      <view class="form-item {{errors.roomId ? 'error' : ''}}">
        <view class="form-label">关联房屋</view>
        <view class="house-selector" bindtap="showHouseSelector">
          <text class="house-value">{{formData.roomName || '请选择关联房屋'}}</text>
          <view class="selector-arrow"></view>
        </view>
        <view class="error-message" wx:if="{{errors.roomId}}">{{errors.roomId}}</view>
      </view>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="bottom-actions">
    <button class="btn-cancel" bindtap="cancelAdd">取消</button>
    <button class="btn-submit" bindtap="submitForm" disabled="{{submitting}}">确认</button>
  </view>

  <!-- 房屋选择器弹窗 -->
  <view class="add-house-modal {{showHouseSelector ? 'show' : ''}}" catchtouchmove="preventTouchMove">
    <view class="modal-mask" bindtap="hideHouseSelector"></view>
    <view class="modal-content">
      <!-- 弹窗头部 -->
      <view class="modal-header">
        <text class="modal-title">选择房屋</text>
        <view class="modal-close" bindtap="hideHouseSelector">×</view>
      </view>

      <!-- 已选择信息显示 -->
      <view class="model-selected-view" wx:if="{{selectedBuildingName || selectedRoomName}}">
        <view class="selection-item-info" wx:if="{{selectedBuildingName}}">
          <text class="selection-label">已选楼栋：</text>
          <text class="selection-value">{{selectedBuildingName}}</text>
          <view class="back-button" wx:if="{{currentStep === 'room'}}" bindtap="backToBuilding">
            <text class="back-text">重新选择</text>
          </view>
        </view>
        <view class="selection-item-info" wx:if="{{selectedRoomName}}">
          <text class="selection-label">已选房间：</text>
          <text class="selection-value">{{selectedUnitNumber}}{{selectedRoomName}}</text>
        </view>
      </view>

      <!-- 弹窗内容 -->
      <view class="modal-body">
        <!-- 搜索框 -->
        <view class="search-container">
          <input
            class="search-input"
            placeholder="{{searchPlaceholder}}"
            value="{{searchKeyword}}"
            bindinput="onSearchInput"
            placeholder-style="color: #999;"
          />
        </view>

        <!-- 动态内容区域 -->
        <scroll-view scroll-y="true" class="content-scroll">
          <!-- 楼栋列表 -->
          <view wx:if="{{currentStep === 'building'}}">
            <view class="selection-grid" wx:if="{{filteredBuildings.length > 0}}">
              <view
                class="selection-item {{item.id === selectedBuildingId ? 'selected' : ''}}"
                wx:for="{{filteredBuildings}}"
                wx:key="id"
                bindtap="selectBuilding"
                data-id="{{item.id}}"
                data-name="{{item.buildingNumber}}"
              >
                <text class="selection-text">{{item.buildingNumber}}</text>
              </view>
            </view>

            <!-- 楼栋空状态 -->
            <view class="empty-state-modal" wx:if="{{filteredBuildings.length === 0}}">
              <view class="empty-icon-modal">🏢</view>
              <view class="empty-text-modal">暂无楼栋信息</view>
              <view class="empty-desc-modal">请联系管理员添加楼栋信息</view>
            </view>
          </view>

          <!-- 房间列表 -->
          <view wx:if="{{currentStep === 'room'}}">
            <view class="selection-grid" wx:if="{{filteredRooms.length > 0}}">
              <view
                class="selection-item {{item.id === selectedRoomId ? 'selected' : ''}}"
                wx:for="{{filteredRooms}}"
                wx:key="id"
                bindtap="selectRoom"
                data-id="{{item.id}}"
                data-name="{{item.roomNumber}}"
                data-unit="{{item.unitNumber}}"
              >
                <text class="selection-text">{{item.unitNumber}}{{item.roomNumber}}</text>
              </view>
            </view>

            <!-- 房间空状态 -->
            <view class="empty-state-modal" wx:if="{{filteredRooms.length === 0}}">
              <view class="empty-icon-modal">🏠</view>
              <view class="empty-text-modal">暂无房间信息</view>
              <view class="empty-desc-modal">请联系管理员添加房间信息</view>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 弹窗底部操作栏 -->
      <view class="modal-footer">
        <button class="btn-cancel" bindtap="hideHouseSelector">取消</button>
        <button
          class="btn-confirm {{selectedRoomId ? 'enabled' : 'disabled'}}"
          bindtap="confirmHouseSelection"
          disabled="{{!selectedRoomId}}"
        >
          确定
        </button>
      </view>
    </view>
  </view>

  <!-- 人员标签选择弹窗 -->
  <view class="tag-modal {{showTagSelector ? 'show' : ''}}" catchtouchmove="preventTouchMove">
    <view class="modal-mask" bindtap="hideTagSelector"></view>
    <view class="tag-content">
      <view class="modal-header">
        <text class="modal-title">选择人员标签</text>
        <view class="modal-close" bindtap="hideTagSelector">
          <text class="close-icon">×</text>
        </view>
      </view>
      <view class="modal-body">
        <scroll-view scroll-y="true" class="tag-scroll">
          <checkbox-group bindchange="onTagCheckboxChange">
            <view class="tag-list">
              <label class="tag-item" wx:for="{{residentTag}}" wx:key="nameEn">
                <checkbox
                  value="{{item.nameEn}}"
                  checked="{{selectedTags.some(tag => tag.nameEn === item.nameEn)}}"
                  class="tag-checkbox"
                />
                <text class="tag-text">{{item.nameCn}}</text>
              </label>
            </view>
          </checkbox-group>
        </scroll-view>
      </view>
      <view class="modal-footer">
        <button class="btn-cancel" bindtap="hideTagSelector">取消</button>
        <button class="btn-confirm enabled" bindtap="confirmTagSelection">确定</button>
      </view>
    </view>
  </view>
</view>