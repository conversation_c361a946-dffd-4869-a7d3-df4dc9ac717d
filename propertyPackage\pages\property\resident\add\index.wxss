/* 添加居民页面样式 */
.container {
  min-height: 100vh;
  background-color: #F2F2F7;
  padding-bottom: 120rpx;
}

/* 表单容器 */
.form-container {
  padding: 24rpx 0;
}

/* 表单分区 */
.form-section {
  background: #FFFFFF;
  border-radius: 28rpx;
  margin: 0 32rpx 24rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
  margin-bottom: 24rpx;
}

/* 表单项 */
.form-item {
  margin-bottom: 24rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 28rpx;
  color: #8E8E93;
  margin-bottom: 12rpx;
}

.form-input {
  height: 88rpx;
  background: #FFFFFF;
  border: 2rpx solid #F2F2F7;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #000000;
}

.form-textarea {
  min-height: 120rpx;
  background: #FFFFFF;
  border: 2rpx solid #F2F2F7;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #000000;
  line-height: 1.4;
}


/* 单选框组 */
.radio-group {
  display: flex;
  height: 88rpx;
  align-items: center;
}

.radio {
  margin-right: 48rpx;
  font-size: 28rpx;
  color: #000000;
  display: flex;
  align-items: center;
}

/* 复选框组 */
.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
}

.checkbox-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  min-width: 200rpx;
}

.checkbox-text {
  font-size: 28rpx;
  color: #000000;
  margin-left: 12rpx;
}

/* 日期选择器 */
.picker-value {
  height: 88rpx;
  background: #FFFFFF;
  border: 2rpx solid #F2F2F7;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #000000;
  display: flex;
  align-items: center;
}

/* 标签选择器 */
.tag-selector {
  height: 88rpx;
  background: #FFFFFF;
  border: 2rpx solid #F2F2F7;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #000000;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tag-value {
  flex: 1;
}

/* 房屋选择器 */
.house-selector {
  height: 88rpx;
  background: #FFFFFF;
  border: 2rpx solid #F2F2F7;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #000000;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.house-value {
  flex: 1;
}

.selector-arrow {
  width: 32rpx;
  height: 32rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

/* 错误状态 */
.form-item.error .form-input,
.form-item.error .picker-value,
.form-item.error .house-selector,
.form-item.error .tag-selector {
  border: 2rpx solid #FF3B30;
}

.error-message {
  font-size: 24rpx;
  color: #FF3B30;
  margin-top: 8rpx;
}

/* 底部操作区 */
.bottom-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  padding: 24rpx 32rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.btn-cancel, .btn-submit {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.btn-cancel {
  background: #FFFFFF;
  color: #8E8E93;
  border: 1rpx solid rgba(142, 142, 147, 0.2);
  margin-right: 24rpx;
}

.btn-submit {
  background: #FF8C00;
  color: #FFFFFF;
}

.btn-submit[disabled] {
  opacity: 0.5;
}

/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(4px);
}

.modal-content {
  position: relative;
  width: 600rpx;
  background: #FFFFFF;
  border-radius: 28rpx;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform 0.3s;
}

.modal.show .modal-content {
  transform: scale(1);
}

.modal-header {
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid rgba(60, 60, 67, 0.1);
}

.modal-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #000000;
  flex: 1;
  text-align: center;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #8E8E93;
  cursor: pointer;
}

.modal-body {
  padding: 32rpx;
  max-height: 600rpx;
}

/* 房屋列表 */
.house-list {
  max-height: 500rpx;
}

.house-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid rgba(60, 60, 67, 0.1);
}

.house-item:last-child {
  border-bottom: none;
}

.house-address {
  font-size: 28rpx;
  color: #000000;
}

.item-selected {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #007AFF;
  font-weight: bold;
}

.house-info {
  flex: 1;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #8E8E93;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #C7C7CC;
}

/* ==================== 房屋选择弹窗样式 ==================== */
.add-house-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.add-house-modal.show {
  opacity: 1;
  visibility: visible;
}

.add-house-modal .modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
}

.add-house-modal .modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-radius: 16rpx 16rpx 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
}

.add-house-modal.show .modal-content {
  transform: translateY(0);
}

.add-house-modal .modal-header {
  padding: 16rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.add-house-modal .modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 已选择信息显示区域 */
.selection-info {
  padding: 16rpx 20rpx;
  background: #f8f9fa;
  border-bottom: 1rpx solid #f0f0f0;
}

.selection-item-info {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.selection-item-info:last-child {
  margin-bottom: 0;
}

.add-house-modal .modal-close {
  position: absolute;
  right: 20rpx;
  font-size: 24rpx;
  color: #999;
  width: 30rpx;
  height: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 15rpx;
}

.add-house-modal .modal-close:active {
  background-color: rgba(0, 0, 0, 0.05);
}

.add-house-modal .modal-body {
  flex: 1;
  padding: 20rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.selection-label {
  font-size: 24rpx;
  color: #8E8E93;
  margin-right: 12rpx;
}

.selection-value {
  font-size: 28rpx;
  color: #000000;
  font-weight: 500;
  flex: 1;
}

.back-button {
  padding: 8rpx 16rpx;
  background: #007AFF;
  border-radius: 8rpx;
}

.back-text {
  font-size: 20rpx;
  color: white;
}

/* 搜索框 */
.search-container {
  padding: 0 0 16rpx 0;
}

.search-input {
  height: 72rpx;
  background: #f5f5f5;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #000000;
  border: 1rpx solid #e0e0e0;
}

/* 内容滚动区域 */
.content-scroll {
  flex: 1;
  overflow-y: auto;
}

/* 选择网格 */
.selection-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
  padding: 16rpx 0;
}

.selection-item {
  height: 80rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid transparent;
  transition: all 0.2s ease;
}

.selection-item.selected {
  background: rgba(0, 122, 255, 0.1);
  border-color: #007AFF;
  color: #007AFF;
  font-weight: 500;
}

.selection-text {
  font-size: 28rpx;
  color: #333;
  text-align: center;
}

.selection-item.selected .selection-text {
  color: #007AFF;
  font-weight: 500;
}

/* 弹窗空状态样式 */
.empty-state-small {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx 20rpx;
  text-align: center;
}

.empty-text-small {
  font-size: 28rpx;
  color: #999;
}

/* 弹窗底部操作栏 */
.add-house-modal .modal-footer {
  padding: 32rpx 40rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  gap: 12rpx;
  background: white;
}

.add-house-modal .btn-cancel,
.add-house-modal .btn-confirm {
  flex: 1;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.add-house-modal .btn-cancel {
  background: #f5f5f5;
  color: #666;
}

.add-house-modal .btn-cancel:active {
  background: #e0e0e0;
  transform: scale(0.98);
}

.add-house-modal .btn-confirm {
  background: #007AFF;
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
}

.add-house-modal .btn-confirm.enabled {
  background: linear-gradient(135deg, #007AFF, #0056b3);
}

.add-house-modal .btn-confirm.enabled:active {
  transform: scale(0.98);
  box-shadow: 0 1rpx 4rpx rgba(0, 122, 255, 0.2);
}

.add-house-modal .btn-confirm.disabled {
  background: #e9ecef;
  color: #adb5bd;
  box-shadow: none;
  cursor: not-allowed;
}

/* ==================== 人员标签弹窗样式 ==================== */
.tag-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.tag-modal.show {
  opacity: 1;
  visibility: visible;
}

.tag-modal .modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
}

.tag-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600rpx;
  max-height: 70vh;
  background: white;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.tag-modal.show .tag-content {
  animation: modalSlideIn 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* 弹窗头部样式 */
.tag-modal .modal-header {
  padding: 40rpx 40rpx 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tag-modal .modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.tag-modal .modal-close {
  width: 64rpx;
  height: 64rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
  background: #f5f5f5;
  transition: all 0.2s ease;
}

.tag-modal .modal-close:active {
  background: #e0e0e0;
  transform: scale(0.95);
}

.close-icon {
  font-size: 20rpx;
  color: #666;
  line-height: 1;
}

/* 弹窗内容区域 */
.tag-modal .modal-body {
  flex: 1;
  padding: 20rpx 24rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.tag-scroll {
  flex: 1;
  max-height: 400rpx;
}

.tag-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.tag-item {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  transition: all 0.2s ease;
}

.tag-item:active {
  background: #e9ecef;
}

.tag-checkbox {
  margin-right: 16rpx;
  transform: scale(1.2);
}

.tag-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

/* 弹窗底部操作栏样式 */
.tag-modal .modal-footer {
  padding: 20rpx 24rpx 24rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  gap: 16rpx;
  background: #fafafa;
}

.tag-modal .btn-cancel,
.tag-modal .btn-confirm {
  flex: 1;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.tag-modal .btn-cancel {
  background: #f5f5f5;
  color: #666;
}

.tag-modal .btn-cancel:active {
  background: #e0e0e0;
  transform: scale(0.98);
}

.tag-modal .btn-confirm {
  background: #007AFF;
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
}

.tag-modal .btn-confirm.enabled {
  background: linear-gradient(135deg, #007AFF, #0056b3);
}

.tag-modal .btn-confirm.enabled:active {
  transform: scale(0.98);
  box-shadow: 0 1rpx 4rpx rgba(0, 122, 255, 0.2);
}

.modal-footer {
  padding: 0;
  border-top: 1rpx solid rgba(60, 60, 67, 0.1);
}

.modal-footer button {
  height: 100rpx;
  border-radius: 0;
  font-size: 32rpx;
  background: transparent;
  color: #FF8C00;
  font-weight: 500;
}

.modal-footer button::after {
  border: none;
}