/* 添加居民页面样式 */
.container {
  min-height: 100vh;
  background-color: #F2F2F7;
  padding-bottom: 120rpx;
}

/* 表单容器 */
.form-container {
  padding: 24rpx 0;
}

/* 表单分区 */
.form-section {
  background: #FFFFFF;
  border-radius: 28rpx;
  margin: 0 32rpx 24rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
  margin-bottom: 24rpx;
}

/* 表单项 */
.form-item {
  margin-bottom: 24rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 28rpx;
  color: #8E8E93;
  margin-bottom: 12rpx;
}

.form-input {
  height: 88rpx;
  background: #FFFFFF;
  border: 2rpx solid #F2F2F7;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #000000;
}

.form-textarea {
  min-height: 120rpx;
  background: #FFFFFF;
  border: 2rpx solid #F2F2F7;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #000000;
  line-height: 1.4;
}


/* 单选框组 */
.radio-group {
  display: flex;
  height: 88rpx;
  align-items: center;
}

.radio {
  margin-right: 48rpx;
  font-size: 28rpx;
  color: #000000;
  display: flex;
  align-items: center;
}

/* 复选框组 */
.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
}

.checkbox-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  min-width: 200rpx;
}

.checkbox-text {
  font-size: 28rpx;
  color: #000000;
  margin-left: 12rpx;
}

/* 日期选择器 */
.picker-value {
  height: 88rpx;
  background: #FFFFFF;
  border: 2rpx solid #F2F2F7;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #000000;
  display: flex;
  align-items: center;
}

/* 标签选择器 */
.tag-selector {
  height: 88rpx;
  background: #FFFFFF;
  border: 2rpx solid #F2F2F7;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #000000;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tag-value {
  flex: 1;
}

/* 房屋选择器 */
.house-selector {
  height: 88rpx;
  background: #FFFFFF;
  border: 2rpx solid #F2F2F7;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #000000;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.house-value {
  flex: 1;
}

.selector-arrow {
  width: 32rpx;
  height: 32rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

/* 错误状态 */
.form-item.error .form-input,
.form-item.error .picker-value,
.form-item.error .house-selector,
.form-item.error .tag-selector {
  border: 2rpx solid #FF3B30;
}

.error-message {
  font-size: 24rpx;
  color: #FF3B30;
  margin-top: 8rpx;
}

/* 底部操作区 */
.bottom-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  padding: 24rpx 32rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.btn-cancel, .btn-submit {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.btn-cancel {
  background: #FFFFFF;
  color: #8E8E93;
  border: 1rpx solid rgba(142, 142, 147, 0.2);
  margin-right: 24rpx;
}

.btn-submit {
  background: #FF8C00;
  color: #FFFFFF;
}

.btn-submit[disabled] {
  opacity: 0.5;
}

/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(4px);
}

.modal-content {
  position: relative;
  width: 600rpx;
  background: #FFFFFF;
  border-radius: 28rpx;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform 0.3s;
}

.modal.show .modal-content {
  transform: scale(1);
}

.modal-header {
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid rgba(60, 60, 67, 0.1);
}

.modal-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #000000;
  flex: 1;
  text-align: center;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #8E8E93;
  cursor: pointer;
}

.modal-body {
  padding: 32rpx;
  max-height: 600rpx;
}

/* 房屋列表 */
.house-list {
  max-height: 500rpx;
}

.house-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid rgba(60, 60, 67, 0.1);
}

.house-item:last-child {
  border-bottom: none;
}

.house-address {
  font-size: 28rpx;
  color: #000000;
}

.item-selected {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #007AFF;
  font-weight: bold;
}

.house-info {
  flex: 1;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #8E8E93;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #C7C7CC;
}

/* ==================== 房屋选择弹窗样式 ==================== */
.add-house-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.add-house-modal.show {
  opacity: 1;
  visibility: visible;
}

.add-house-modal .modal-content {
  transform: scale(0.8);
  transition: transform 0.3s ease;
}

.add-house-modal.show .modal-content {
  transform: scale(1);
}

/* 已选择信息显示 */
.selection-info {
  padding: 24rpx 32rpx;
  background: #F8F9FA;
  border-bottom: 1rpx solid rgba(60, 60, 67, 0.1);
}

.selection-item-info {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.selection-item-info:last-child {
  margin-bottom: 0;
}

.selection-label {
  font-size: 24rpx;
  color: #8E8E93;
  margin-right: 12rpx;
}

.selection-value {
  font-size: 28rpx;
  color: #000000;
  font-weight: 500;
  flex: 1;
}

.back-button {
  padding: 8rpx 16rpx;
  background: #007AFF;
  border-radius: 8rpx;
}

.back-text {
  font-size: 24rpx;
  color: #FFFFFF;
}

/* 搜索框 */
.search-container {
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid rgba(60, 60, 67, 0.1);
}

.search-input {
  height: 72rpx;
  background: #F2F2F7;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #000000;
}

/* 内容滚动区域 */
.content-scroll {
  max-height: 600rpx;
  padding: 24rpx 32rpx;
}

/* 选择网格 */
.selection-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
}

.selection-item {
  flex: 0 0 calc(25% - 18rpx);
  height: 80rpx;
  background: #F2F2F7;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid transparent;
  transition: all 0.2s ease;
}

.selection-item.selected {
  background: #E3F2FD;
  border-color: #007AFF;
}

.selection-text {
  font-size: 28rpx;
  color: #000000;
  text-align: center;
}

.selection-item.selected .selection-text {
  color: #007AFF;
  font-weight: 500;
}

/* ==================== 人员标签弹窗样式 ==================== */
.tag-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.tag-modal.show {
  opacity: 1;
  visibility: visible;
}

.tag-modal .modal-content {
  transform: scale(0.8);
  transition: transform 0.3s ease;
}

.tag-modal.show .modal-content {
  transform: scale(1);
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
  padding: 24rpx 0;
}

.tag-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 24rpx;
  background: #F2F2F7;
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  min-width: 120rpx;
  transition: all 0.2s ease;
}

.tag-item.selected {
  background: #E3F2FD;
  border-color: #007AFF;
}

.tag-text {
  font-size: 28rpx;
  color: #000000;
}

.tag-item.selected .tag-text {
  color: #007AFF;
  font-weight: 500;
}

.tag-check {
  font-size: 24rpx;
  color: #007AFF;
  margin-left: 12rpx;
}

.modal-footer {
  padding: 0;
  border-top: 1rpx solid rgba(60, 60, 67, 0.1);
}

.modal-footer button {
  height: 100rpx;
  border-radius: 0;
  font-size: 32rpx;
  background: transparent;
  color: #FF8C00;
  font-weight: 500;
}

.modal-footer button::after {
  border: none;
}