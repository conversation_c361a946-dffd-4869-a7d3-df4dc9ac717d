// 访客凭证页面

const visitorsApi = require('@/api/visitorsApi')
const util = require('@/utils/util')
const dateUtil = require('@/utils/dateUtil')
var QR = require("@/utils/qrcode.js");
Page({
  data: {

    visitorId: '', // 访客ID
    visitorData: {}, // 访客数据
    communityName: '', // 小区名称
    communityType: 'normal', // 小区类型：normal-普通小区，smart-智能小区
    statusText: '', // 状态文本
    expireTime: '', // 过期时间
    currentTime: '', // 当前时间
    usageTip: '', // 使用提示
    showShareModal: false, // 是否显示分享弹窗
    showExtendModal: false, // 是否显示延期弹窗
    showNetworkError: false, // 是否显示网络错误提示
    networkErrorMsg: '网络连接失败，请重试', // 网络错误提示信息
    qrcodeGenerated: false, // 二维码是否已生成
    isActive: false, // 是否已生效
    extendDuration: 2, // 延期时长（小时）
    newExpireTime: '', // 延期后的过期时间
    retryOperation: null, // 重试操作函数
    activeTabIndex: 0, // 当前激活的底部标签索引
    visitorStatusDict: [],
    imagePath: '',
    // 核销相关
    fromScan: false, // 是否从扫码进入
    showVerifyButton: false, // 是否显示核销按钮
    isVerifying: false, // 是否正在核销
    verifySuccess: false // 核销是否成功
  },

  onLoad: function (options) {

    // 获取访客ID
    const visitorId = options.id;
    if (!visitorId) {
      wx.showToast({
        title: '访客ID不存在',
        icon: 'none'
      });

      return;
    }

    // 检查是否从扫码进入
    const fromScan = options.fromScan === 'true';

    this.setData({
      visitorId: visitorId,
      fromScan: fromScan
    });

    // 获取小区信息
    this.getCommunityInfo();

    this.getVisitorStatus()

    // 获取访客数据
    this.getVisitorData();

    // 更新当前时间
    this.updateCurrentTime();
    this.timer = setInterval(() => {
      this.updateCurrentTime();
    }, 1000);

    // 如果是从分享进入，显示分享选项
    if (options.share) {
      this.setData({
        showShareModal: true
      });
    }

    // 如果是从扫码进入，检查是否显示核销按钮
    if (fromScan) {
      this.checkShowVerifyButton();
    }
  },


  getVisitorStatus() {
    try {

      const visitorStatusDict = util.getDictByNameEn('visitor_status')
      if (visitorStatusDict && visitorStatusDict.length > 0 && visitorStatusDict[0].children) {
        // 将字典数据转换为状态映射对象


        this.setData({
          visitorStatusDict: visitorStatusDict[0].children
        })

        console.log('访客状态字典：', this.data.visitorStatusDict)

      } else {

      }
    } catch (error) {
      console.error('获取访客状态字典失败：', error)

    }
  },


  onUnload: function () {
    // 清除定时器
    if (this.timer) {
      clearInterval(this.timer);
    }
  },

  // 检查是否显示核销按钮
  checkShowVerifyButton: function () {
    // 检查用户是否是已认证的物业人员
    const propertyResult = util.checkPropertyAuthenticated();

    if (!propertyResult.isPropertyAuthenticated) {
      console.log('用户不是已认证的物业人员，不显示核销按钮');
      return;
    }

    // 如果访客数据已加载，立即检查；否则等待数据加载完成后检查
    if (this.data.visitorData && this.data.visitorData.id) {
      this.checkVisitorExpiration();
    }
  },

  // 检查访客是否过期
  checkVisitorExpiration: function () {
    const { visitorData } = this.data;

    if (!visitorData || !visitorData.visitTime) {
      return;
    }

    // 计算过期时间
    const expireDate = dateUtil.addHoursToTime(visitorData.visitTime, visitorData.stayDuration);
    const now = new Date();
    const isExpired = now > new Date(expireDate);

    console.log('访客过期检查：', {
      visitTime: visitorData.visitTime,
      stayDuration: visitorData.stayDuration,
      expireDate: expireDate,
      now: now.toISOString(),
      isExpired: isExpired,
      status: visitorData.status
    });

    // 如果没有过期且状态为待到访，显示核销按钮
    const showVerifyButton = !isExpired && visitorData.status === 'wait_visit';

    this.setData({
      showVerifyButton: showVerifyButton
    });
  },

  // 获取访客数据
  getVisitorData: function () {
    var that = this

    // 判断是否是物业员工身份扫码进入
    const isPropertyStaff = this.data.fromScan && util.checkPropertyAuthenticated().isPropertyAuthenticated;

    // 根据身份选择不同的API接口
    const apiCall = isPropertyStaff ?
      visitorsApi.properGetVisitorDetail(this.data.visitorId) :
      visitorsApi.getVisitorDetail(this.data.visitorId);

    console.log('获取访客详情，使用接口：', isPropertyStaff ? 'properGetVisitorDetail' : 'getVisitorDetail');
    debugger
    apiCall.then(res => {
      debugger
      console.log('访客信息', res);


      if (res) {
        var visitorData = res
        // 计算状态文本

        var visitorStatusDict = that.data.visitorStatusDict
        const status = visitorStatusDict.filter(item => item.nameEn === visitorData.status)

        // 计算过期时间
        const expireDate = dateUtil.addHoursToTime(visitorData.visitTime,visitorData.stayDuration);
        const expireTime = expireDate

        // 设置使用提示
        const communityType = wx.getStorageSync('communityType') || 'normal';
        let usageTip = '';
        if (communityType === 'smart') {
          usageTip = '请在闸机处亮屏通行，系统将自动识别';
        } else {
          usageTip = '请向门卫出示此二维码，由门卫扫码核验';
        }

        // 判断是否已生效
        const now = new Date();

        const startDate = dateUtil.parseDateTime(visitorData.date, visitorData.startTime);
        const isActive = now >= startDate;

        this.setData({
          visitorData: visitorData,
          statusText: status[0].nameCn,
          expireTime: expireTime,
          usageTip: usageTip,
          communityType: communityType,
          isActive: isActive
        });

        // 生成二维码
        that.generateQrcode();

        // 如果是从扫码进入，检查是否显示核销按钮
        if (that.data.fromScan) {
          that.checkVisitorExpiration();
        }

      } else {
        console.error('获取访客详情失败：', res);
        this.setData({
          visitors: []
        });
        debugger
        wx.showToast({
          title: res&&res.errMessage?res.errMessage : '获取访客详情失败',
          icon: 'none'
        });
      }
    })
      .catch(err => {
        console.error('获取访客详情异常：', err);
        this.setData({
          visitors: []
        });
       
      });


  },

  // 获取小区信息
  getCommunityInfo: function () {
    const communityInfo = wx.getStorageSync('selectedCommunity')

    const communityName = communityInfo.communityName

    this.setData({
      communityName: communityName
    });
  },

  // 更新当前时间
  updateCurrentTime: function () {
    const now = new Date();
    const currentTime = now.toLocaleTimeString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });

    this.setData({
      currentTime: currentTime
    });

    // 检查凭证是否过期
    this.checkExpiration();
  },

  // 检查凭证是否过期
  checkExpiration: function () {
    const { visitorData } = this.data;
    if (visitorData.status !== 'pending') {
      return;
    }

    const now = new Date();

    const expireDate = dateUtil.parseDateTime(visitorData.date, visitorData.endTime);

    if (now > expireDate) {
      // 更新访客状态为已过期
      this.updateVisitorStatus('expired');

      wx.showToast({
        title: '访客凭证已过期',
        icon: 'none'
      });
    }
  },

  // 更新访客状态
  updateVisitorStatus: function (status) {
    if (VisitorManager.updateVisitorStatus(this.data.visitorId, status)) {
      // 更新页面数据
      this.setData({
        'visitorData.status': status,
        statusText: this.data.visitorStatusDict[status] || '未知状态'
      });
    }
  },

  // 生成二维码
  generateQrcode: function () {


    var size = this.setCanvasSize(); //动态设置画布大小


    try {
      if (this.data.visitorData.id)
        this.createQrCode('visitor:' + this.data.visitorData.id, "mycanvas", size.w, size.h);


    } catch (error) {
      console.error('生成二维码失败', error);
      this.showNetworkError('生成二维码失败，请重试', () => {
        this.generateQrcode();
      });
    }


  },


  //适配不同屏幕大小的canvas
  setCanvasSize: function () {
    var size = {};
    try {
       
      var res = wx.getWindowInfo();
      var scale = 812 / 475; //不同屏幕下canvas的适配比例；设计稿是750宽
      var width = res.windowWidth / scale;
      var height = width; //canvas画布为正方形
      size.w = width;
      size.h = height;
    } catch (e) {
       
      // Do something when catch error
      console.log("获取设备信息失败" + e);
    }
    return size;
  },
  createQrCode: function (url, canvasId, cavW, cavH) {

    //调用插件中的draw方法，绘制二维码图片
    setTimeout(() => { QR.api.draw(url, canvasId, cavW, cavH, this, this.canvasToTempImage); }, 100);

  },


  //获取临时缓存照片路径，存入data中
  canvasToTempImage: function () {
    var that = this;

    wx.canvasToTempFilePath({
      canvasId: 'mycanvas',
      success: function (res) {

        var tempFilePath = res.tempFilePath;
        console.log(tempFilePath);

        that.setData({
          imagePath: tempFilePath,
          qrcodeGenerated: true
        });
      },
      fail: function (res) {
        console.log(res);
      }
    }, that);
  },

  imgYu: function (event) {

    var that = this
    var src = that.data.imagePath;
    if (src != null) {

      var imgList = [src];
      wx.previewImage({
        current: src, // 当前显示图片的http链接
        urls: imgList // 需要预览的图片http链接列表
      })
    }
  },

  // 显示网络错误提示
  showNetworkError: function (message, retryFunc) {
    this.setData({
      showNetworkError: true,
      networkErrorMsg: message || '网络连接失败，请重试',
      retryOperation: retryFunc
    });
  },

  // 隐藏网络错误提示
  hideNetworkError: function () {
    this.setData({
      showNetworkError: false
    });
  },

  // 重试操作
  retryOperation: function () {
    this.hideNetworkError();

    if (typeof this.data.retryOperation === 'function') {
      setTimeout(() => {
        this.data.retryOperation();
      }, 300);
    }
  },

  // 保存凭证
  saveCredential: function () {
    if (!this.data.qrcodeGenerated) {
      wx.showToast({
        title: '二维码生成中，请稍候',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '保存中...',
    });

    // 保存图片到相册
    wx.saveImageToPhotosAlbum({
      filePath: this.data.imagePath,
      success: () => {
        wx.hideLoading();
        wx.showToast({
          title: '凭证已保存到相册',
          icon: 'success'
        });
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('保存失败', err);

        if (err.errMsg.indexOf('auth deny') !== -1) {
          wx.showModal({
            title: '提示',
            content: '需要您授权保存图片到相册',
            confirmText: '去授权',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting();
              }
            }
          });
        } else {
          this.showNetworkError('保存失败，请重试', () => {
            this.saveCredential();
          });
        }
      }
    });
  },

  // 显示分享选项
  showShareOptions: function () {
    this.setData({
      showShareModal: true
    });
  },

  // 隐藏分享弹窗
  hideShareModal: function () {
    this.setData({
      showShareModal: false
    });
  },

  // 分享到微信
  shareToWechat: function () {
    // 微信小程序分享功能需要通过onShareAppMessage实现
    // 这里仅作示例
    wx.showToast({
      title: '请点击右上角分享',
      icon: 'none'
    });
    this.hideShareModal();
  },

  // 复制链接
  copyLink: function () {
    const { visitorData } = this.data;

    // 构建分享链接
    const shareLink = `https://example.com/visitor?id=${visitorData.id}`;

    wx.setClipboardData({
      data: shareLink,
      success: () => {
        wx.showToast({
          title: '链接已复制',
          icon: 'success'
        });
        this.hideShareModal();
      }
    });
  },

  // 显示作废确认
  showCancelConfirm: function () {
    wx.showModal({
      title: '作废凭证',
      content: '确定要作废此访客凭证吗？作废后将无法恢复。',
      confirmText: '确定作废',
      confirmColor: '#FF3B30',
      success: (res) => {
        if (res.confirm) {
          this.cancelCredential();
        }
      }
    });
  },

  // 作废凭证
  cancelCredential: function () {
    this.updateVisitorStatus('canceled');

    wx.showToast({
      title: '凭证已作废',
      icon: 'success'
    });
  },

  // 核销访客
  verifyVisitor: function () {
    if (this.data.isVerifying) {
      return;
    }

    wx.showModal({
      title: '确认核销',
      content: '确定要核销此访客吗？核销后访客状态将变为已到访。',
      confirmText: '确认核销',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.performVerify();
        }
      }
    });
  },

  // 执行核销操作
  performVerify: function () {
    this.setData({
      isVerifying: true
    });

    wx.showLoading({
      title: '核销中...',
      mask: true
    });

    // 调用核销API
    visitorsApi.verifyVisitor(this.data.visitorId)
      .then(res => {
        console.log('核销结果：', res);

        wx.hideLoading();

        // 核销成功
        this.setData({
          isVerifying: false,
          verifySuccess: true,
          showVerifyButton: false
        });

        wx.showToast({
          title: '核销成功',
          icon: 'success',
          duration: 2000
        });

        // 重新请求详情接口更新数据状态
        this.refreshVisitorData();

      })
      .catch(err => {
        console.error('核销失败：', err);

        wx.hideLoading();

        this.setData({
          isVerifying: false
        });

      });
  },

  // 刷新访客数据
  refreshVisitorData: function () {
    console.log('核销成功后刷新访客数据...');

    // 判断是否是物业员工身份扫码进入
    const isPropertyStaff = this.data.fromScan && util.checkPropertyAuthenticated().isPropertyAuthenticated;

    // 根据身份选择不同的API接口
    const apiCall = isPropertyStaff ?
      visitorsApi.properGetVisitorDetail(this.data.visitorId) :
      visitorsApi.getVisitorDetail(this.data.visitorId);

    // 重新获取访客详情
    apiCall
      .then(res => {
        console.log('刷新后的访客信息：', res);

        if (res) {
          const visitorData = res;

          // 计算状态文本
          const statusTextMap = this.data.statusTextMap;
          const status = statusTextMap.filter(item => item.nameEn === visitorData.status);

          // 计算过期时间
          const expireDate = dateUtil.addHoursToTime(visitorData.visitTime, visitorData.stayDuration);
          const expireTime = expireDate;

          // 更新访客数据
          this.setData({
            visitorData: visitorData,
            statusText: status.length > 0 ? status[0].nameCn : '未知状态',
            expireTime: expireTime
          });

          console.log('访客数据刷新完成，新状态：', visitorData.status);
        }
      })
      .catch(err => {
        console.error('刷新访客数据失败：', err);
        // 刷新失败不影响核销成功的提示，只记录日志
      });
  },

  // 切换收藏状态
  toggleFavorite: function () {
    const { visitorData } = this.data;

    if (!visitorData || !visitorData.id) {
      wx.showToast({
        title: '访客信息不存在',
        icon: 'none'
      });
      return;
    }

    // 切换收藏状态
    const newIsUsual = !visitorData.isUsual;

    // 准备API参数
    const params = {
      id: visitorData.id,
      isUsual: newIsUsual
    };

    console.log('切换收藏状态参数：', params);

    // 调用设为常用访客接口
    visitorsApi.editVisitUse(params)
      .then(res => {
        console.log('切换收藏状态结果：', res);

        // 更新本地数据
        this.setData({
          'visitorData.isUsual': newIsUsual
        });

        wx.showToast({
          title: newIsUsual ? '已收藏' : '已取消收藏',
          icon: 'success'
        });
      })
      .catch(err => {
        console.error('切换收藏状态失败：', err);

        wx.showToast({
          title: err.message || '操作失败，请重试',
          icon: 'none'
        });
      });
  },

  // 更新状态文本
  updateStatusText: function (status) {
    const visitorStatusDict = this.data.visitorStatusDict;
    const statusItem = visitorStatusDict.find(item => item.nameEn === status);

    if (statusItem) {
      this.setData({
        statusText: statusItem.nameCn
      });
    }
  },



  // 显示延期选项
  showExtendOptions: function () {
    // 计算新的过期时间
    this.calculateNewExpireTime(this.data.extendDuration);

    this.setData({
      showExtendModal: true
    });
  },

  // 隐藏延期弹窗
  hideExtendModal: function () {
    this.setData({
      showExtendModal: false
    });
  },

  // 选择延期时长
  selectExtendDuration: function (e) {
    const duration = parseInt(e.currentTarget.dataset.duration);

    // 计算新的过期时间
    this.calculateNewExpireTime(duration);

    this.setData({
      extendDuration: duration
    });
  },

  // 计算新的过期时间
  calculateNewExpireTime: function (hours) {
    const { visitorData } = this.data;

    // 解析原始过期时间

    const originalExpireDate = dateUtil.parseDateTime(visitorData.date, visitorData.endTime);

    // 计算新的过期时间
    const newExpireDate = new Date(originalExpireDate.getTime() + hours * 60 * 60 * 1000);

    // 格式化新的过期时间
    const newExpireTime = newExpireDate.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });

    this.setData({
      newExpireTime: newExpireTime
    });
  },

  // 确认延期
  confirmExtend: function () {
    const { visitorData, extendDuration } = this.data;

    wx.showLoading({
      title: '处理中...',
    });

    try {
      // 解析原始过期时间
    
      const originalExpireDate = dateUtil.parseDateTime(visitorData.date, visitorData.endTime);

      // 计算新的过期时间
      const newExpireDate = new Date(originalExpireDate.getTime() + extendDuration * 60 * 60 * 1000);

      // 格式化新的结束时间
      const newEndTime = newExpireDate.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });

      // 更新访客数据
      const updatedVisitor = {
        ...visitorData,
        endTime: newEndTime
      };

      // 保存更新后的访客数据
      if (VisitorManager.updateVisitor(updatedVisitor)) {
        wx.hideLoading();

        // 更新页面数据
        this.setData({
          'visitorData.endTime': newEndTime,
          expireTime: newEndTime,
          showExtendModal: false
        });

        // 重新生成二维码
        this.generateQrcode();

        wx.showToast({
          title: '延期成功',
          icon: 'success'
        });
      } else {
        throw new Error('更新访客数据失败');
      }
    } catch (error) {
      wx.hideLoading();
      console.error('延期失败', error);

      this.showNetworkError('延期失败，请重试', () => {
        this.confirmExtend();
      });
    }
  },

  // 立即生效
  activateNow: function () {
    const { visitorData } = this.data;

    wx.showLoading({
      title: '处理中...',
    });

    try {
      // 获取当前时间
      const now = new Date();

      // 格式化当前时间为开始时间
      const newStartTime = now.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });

      // 更新访客数据
      const updatedVisitor = {
        ...visitorData,
        startTime: newStartTime
      };

      // 保存更新后的访客数据
      if (VisitorManager.updateVisitor(updatedVisitor)) {
        wx.hideLoading();

        // 更新页面数据
        this.setData({
          'visitorData.startTime': newStartTime,
          isActive: true
        });

        // 重新生成二维码
        this.generateQrcode();

        wx.showToast({
          title: '已立即生效',
          icon: 'success'
        });
      } else {
        throw new Error('更新访客数据失败');
      }
    } catch (error) {
      wx.hideLoading();
      console.error('立即生效失败', error);

      this.showNetworkError('操作失败，请重试', () => {
        this.activateNow();
      });
    }
  },



  // 分享功能
  onShareAppMessage: function () {
    const { visitorData, communityName } = this.data;

    return {
      title: `${communityName}访客凭证 - ${visitorData.name}`,
      path: `/pages/visitor/credential/index?id=${visitorData.id}`,
      imageUrl: '/images/share-visitor.png' // 分享图片
    };
  }
});
