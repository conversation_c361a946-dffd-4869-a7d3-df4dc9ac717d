
const REQUEST = require('@/utils/request.js')

//获取员工列表
function getPersonList(params)
{
 return	REQUEST.request('/manage-api/v1/person/page', 'GET',params, true)
}

//获取员工信息
function personInfo(personId)
{

	return REQUEST.request('/manage-api/v1/person?id='+personId, 'GET',{}, true)
}

function editPerson(personInfo)
{
	return REQUEST.request('/manage-api/v1/person', 'PUT',personInfo, true)
	
}

//获取物业工作台菜单
function getPropertyMenuList(params)
{
	return REQUEST.request('/manage-api/v1/menu/load-menu', 'get',params, true)
}

//获取组织列表
function getOrgTree()
{
	var params={
		pageNum:1,
		pageSize:500
	}
	return REQUEST.request('/manage-api/v1/org/page','GET',params,true)
}


//获取居民总人数和今日新增人数
function getPersonCount(params)
{
	return REQUEST.request('/manage-api/v1/community/resident/number-count','GET',params,true)
}

//获取居民待审核列表(房屋审核,车辆审核)
function getExamineList(params)
{
	return REQUEST.request('/manage-api/v1/community/resident/examine-page','GET',params,true)
}

//居民房产审核
function examineResidentRoom(params)
{
	return REQUEST.request('/manage-api/v1/community/resident/room/examine','PUT',params,true)
}

//居民车辆审核
function examineResidentVehicle(params)
{
	return REQUEST.request('/manage-api/v1/community/vehicle','PUT',params,true)
}

//新增居民
function addResident(params)
{
	return REQUEST.request('/manage-api/v1/community/resident','POST',params,true)
}

//ju

//获取居民统计
function getResidentStatistics(params)
{
	return REQUEST.request('/manage-api/v1/community/resident/data-count','GET',params,true)
}

//今日访客统计
function getTodayVisitorStatistics(params)
{
	return REQUEST.request('/manage-api/v1/community/visitor/today-visitor','GET',params,true)
}

//访客数据统计
function getVisitorStatistics(params)
{
	return REQUEST.request('/manage-api/v1/community/visitor/data-count','GET',params,true)
}

//今日工单待办统计
function getTodayWorkOrderStatistics(params)
{
	return REQUEST.request('/manage-api/v1/community/work-order/today-wait','GET',params,true)
}

//房屋数据统计
function getRoomDataStatistics(params)
{
	return REQUEST.request('/manage-api/v1/community/room/data-count','GET',params,true)
}

//车辆数据统计
function getVehicleDataStatistics(params)
{
	return REQUEST.request('/manage-api/v1/community/vehicle/data-count','GET',params,true)
}

module.exports={
	getPersonList,
	getOrgTree,
	personInfo,
	editPerson,
	getPropertyMenuList,
	getPersonCount,
	getExamineList,
	examineResidentRoom,
	examineResidentVehicle,
	addResident,
	getResidentStatistics,
	getTodayVisitorStatistics,
	getTodayWorkOrderStatistics,
	getRoomDataStatistics,
	getVehicleDataStatistics,
	getVisitorStatistics
}