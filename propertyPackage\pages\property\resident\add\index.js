// pages/property/resident/add/index.js
const util = require('../../../../../utils/util.js')
const propertyApi = require('../../../../../api/propertyApi.js')

Page({
  data: {
    formData: {
      residentName: '',
      phone: '',
      idCardNumber: '',
      gender: 'man',
      birthday: '',
      certificateType: 'id_card',
      residentType: 'owner',
      roomId: '',
      roomName: '',
      tags: [],
      note: ''
    },
    // 字典数据
    residentTag: [], // 人员标签字典
    certificateType: [], // 证件类型字典
    residentType: [], // 居民住户类型字典
    genderDict: [], // 性别字典

    showHouseSelector: false, // 是否显示房屋选择器
    houses: [], // 可选房屋列表
    submitting: false, // 是否正在提交
    errors: {} // 表单错误信息
  },

  onLoad: function(options) {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '添加居民'
    });

    // 初始化字典数据
    this.initDictData();

    // 如果有传入的房屋ID，则预先填充
    if (options.houseId) {
      this.loadHouseInfo(options.houseId);
    }

    // 加载可选房屋列表
    this.loadHouses();
  },

  // 初始化字典数据
  initDictData: function() {
    try {
      // 获取人员标签字典 (注意：您提供的是vehicle_status，应该是resident_tag)
      const residentTag = util.getDictByNameEn('resident_tag')[0].children || [];

      // 获取证件类型字典
      const certificateType = util.getDictByNameEn('certificate_type')[0].children || [];

      // 获取居民住户类型字典
      const residentType = util.getDictByNameEn('resident_type')[0].children || [];

      // 获取性别字典 (注意：您提供的是resident_type，应该是gender)
      const genderDict = util.getDictByNameEn('gender')[0].children || [];

      this.setData({
        residentTag: residentTag,
        certificateType: certificateType,
        residentType: residentType,
        genderDict: genderDict
      });

      console.log('字典数据加载完成:', {
        residentTag: residentTag.length,
        certificateType: certificateType.length,
        residentType: residentType.length,
        genderDict: genderDict.length
      });
    } catch (error) {
      console.error('字典数据加载失败:', error);
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      });
    }
  },

  // 加载房屋信息
  loadHouseInfo: function(houseId) {
    // 根据房屋ID获取房屋信息
    const houses = this.data.houses;
    const house = houses.find(h => h.id == houseId);

    if (house) {
      const formData = this.data.formData;
      formData.roomId = house.id;
      formData.roomName = house.fullName;

      this.setData({
        formData: formData
      });
    }
  },

  // 加载可选房屋列表
  loadHouses: function() {
    const communityId = wx.getStorageSync('selectedCommunity').id;
    if (!communityId) {
      wx.showToast({
        title: '请先选择小区',
        icon: 'none'
      });
      return;
    }

    // 使用真实API获取房屋列表
    propertyApi.getRoomList({ communityId: communityId }).then(res => {
      if (res.code === 0 && res.data) {
        const houses = res.data.map(room => ({
          id: room.id,
          fullName: `${room.buildingNumber}${room.unitNumber ? room.unitNumber : ''}${room.roomNumber}`,
          buildingNumber: room.buildingNumber,
          unitNumber: room.unitNumber,
          roomNumber: room.roomNumber
        }));

        this.setData({
          houses: houses
        });
      } else {
        console.error('获取房屋列表失败:', res);
        wx.showToast({
          title: '获取房屋列表失败',
          icon: 'none'
        });
      }
    }).catch(error => {
      console.error('获取房屋列表异常:', error);
      wx.showToast({
        title: '网络异常',
        icon: 'none'
      });
    });
  },

  // 输入表单数据
  onInput: function(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    const formData = this.data.formData;

    // 更新对应字段
    formData[field] = value;

    // 清除该字段的错误信息
    const errors = this.data.errors;
    if (errors[field]) {
      delete errors[field];
    }

    this.setData({
      formData: formData,
      errors: errors
    });
  },

  // 选择性别
  onGenderChange: function(e) {
    const formData = this.data.formData;
    const index = e.detail.value;
    const genderDict = this.data.genderDict;

    if (index >= 0 && index < genderDict.length) {
      formData.gender = genderDict[index].nameEn;

      this.setData({
        formData: formData
      });
    }
  },

  // 选择出生日期
  onBirthDateChange: function(e) {
    const formData = this.data.formData;
    formData.birthday = e.detail.value;

    // 清除该字段的错误信息
    const errors = this.data.errors;
    if (errors.birthday) {
      delete errors.birthday;
    }

    this.setData({
      formData: formData,
      errors: errors
    });
  },

  // 选择证件类型
  onCertificateTypeChange: function(e) {
    const formData = this.data.formData;
    const index = e.detail.value;
    const certificateType = this.data.certificateType;

    if (index >= 0 && index < certificateType.length) {
      formData.certificateType = certificateType[index].nameEn;

      this.setData({
        formData: formData
      });
    }
  },

  // 选择居民类型
  onResidentTypeChange: function(e) {
    const formData = this.data.formData;
    const index = e.detail.value;
    const residentType = this.data.residentType;

    if (index >= 0 && index < residentType.length) {
      formData.residentType = residentType[index].nameEn;

      this.setData({
        formData: formData
      });
    }
  },

  // 选择标签
  onTagChange: function(e) {
    const values = e.detail.value;
    const residentTag = this.data.residentTag;
    const selectedTags = values.map(index => residentTag[index].nameEn);

    const formData = this.data.formData;
    formData.tags = selectedTags;

    this.setData({
      formData: formData
    });
  },

  // 显示房屋选择器
  showHouseSelector: function() {
    this.setData({
      showHouseSelector: true
    });
  },

  // 隐藏房屋选择器
  hideHouseSelector: function() {
    this.setData({
      showHouseSelector: false
    });
  },

  // 选择房屋
  selectHouse: function(e) {
    const { id, fullname } = e.currentTarget.dataset;
    const formData = this.data.formData;

    formData.roomId = id;
    formData.roomName = fullname;

    // 清除该字段的错误信息
    const errors = this.data.errors;
    if (errors.roomId) {
      delete errors.roomId;
    }

    this.setData({
      formData: formData,
      errors: errors,
      showHouseSelector: false
    });
  },

  // 验证表单
  validateForm: function() {
    const formData = this.data.formData;
    const errors = {};

    // 验证姓名
    if (!formData.residentName.trim()) {
      errors.residentName = '请输入姓名';
    }

    // 验证手机号
    if (!formData.phone.trim()) {
      errors.phone = '请输入手机号';
    } else if (!/^1\d{10}$/.test(formData.phone)) {
      errors.phone = '手机号格式不正确';
    }

    // 验证证件号码
    if (!formData.idCardNumber.trim()) {
      errors.idCardNumber = '请输入证件号码';
    } else if (formData.certificateType === 'id_card' && !/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(formData.idCardNumber)) {
      errors.idCardNumber = '身份证号格式不正确';
    }

    // 验证出生日期
    if (!formData.birthday) {
      errors.birthday = '请选择出生日期';
    }

    // 验证房屋
    if (!formData.roomId) {
      errors.roomId = '请选择关联房屋';
    }

    this.setData({
      errors: errors
    });

    return Object.keys(errors).length === 0;
  },

  // 提交表单
  submitForm: function() {
    // 验证表单
    if (!this.validateForm()) {
      wx.showToast({
        title: '请完善表单信息',
        icon: 'none'
      });
      return;
    }

    this.setData({
      submitting: true
    });

    const formData = this.data.formData;
    const communityId = wx.getStorageSync('selectedCommunity').id;

    // 构建提交参数
    const params = {
      certificateType: formData.certificateType,
      communityId: communityId,
      idCardNumber: formData.idCardNumber,
      note: formData.note || '',
      phone: formData.phone,
      residentName: formData.residentName,
      residentType: formData.residentType,
      roomId: formData.roomId,
      status: 'normal',
      tags: formData.tags.join(','),
      birthday: formData.birthday,
      gender: formData.gender
    };

    console.log('提交参数:', params);

    // 调用添加居民API
    propertyApi.addResident(params).then(res => {
      this.setData({
        submitting: false
      });

      if (res.code === 0) {
        wx.showToast({
          title: '添加成功',
          icon: 'success'
        });

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        wx.showToast({
          title: res.message || '添加失败',
          icon: 'none'
        });
      }
    }).catch(error => {
      console.error('添加居民失败:', error);
      this.setData({
        submitting: false
      });

      wx.showToast({
        title: '网络异常，请重试',
        icon: 'none'
      });
    });
  },

  // 取消添加
  cancelAdd: function() {
    wx.navigateBack();
  },

  // 阻止滑动穿透
  preventTouchMove: function() {
    return false;
  }
})