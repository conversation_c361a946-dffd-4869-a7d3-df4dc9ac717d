// pages/property/resident/add/index.js
const util = require('../../../../../utils/util.js')
const propertyApi = require('../../../../../api/propertyApi.js')
const houseApi = require('../../../../../api/houseApi.js')

Page({
  data: {
    formData: {
      residentName: '',
      phone: '',
      idCardNumber: '',
      gender: 'man',
      birthday: '',
      certificateType: 'id_card',
      residentType: 'owner',
      roomId: '',
      roomName: '',
      tags: [],
      note: ''
    },
    // 字典数据
    residentTag: [], // 人员标签字典
    certificateType: [], // 证件类型字典
    residentType: [], // 居民住户类型字典
    genderDict: [], // 性别字典

    // 房屋选择弹窗相关
    showHouseSelector: false,
    currentStep: 'building', // 'building' | 'unit' | 'room'
    searchKeyword: '',
    searchPlaceholder: '搜索楼栋',

    // 楼栋、单元和房间数据
    buildings: [],
    filteredBuildings: [],
    rooms: [],
    filteredRooms: [],

    // 选中的数据
    selectedBuildingId: '',
    selectedBuildingName: '',
    selectedUnitNumber: '',
    selectedRoomId: '',
    selectedRoomName: '',

    // 人员标签弹窗
    showTagSelector: false,
    selectedTags: [],
    selectedTagsText: '', // 选中标签的显示文本
    residentTagWithSelected: [], // 带选中状态的标签列表

    submitting: false, // 是否正在提交
    errors: {} // 表单错误信息
  },

  onLoad: function(options) {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '添加居民'
    });

    // 初始化字典数据
    this.initDictData();

    // 如果有传入的房屋ID，则预先填充
    if (options.houseId) {
      this.loadHouseInfo(options.houseId);
    }

    // 加载可选房屋列表
    this.loadHouses();
  },

  // 初始化字典数据
  initDictData: function() {
    try {
      // 获取人员标签字典
      const residentTag = util.getDictByNameEn('resident_tag')[0].children || [];

      // 获取证件类型字典
      const certificateType = util.getDictByNameEn('certificate_type')[0].children || [];

      // 获取居民住户类型字典
      const residentType = util.getDictByNameEn('resident_type')[0].children || [];

      // 获取性别字典
      const genderDict = util.getDictByNameEn('gender')[0].children || [];

      this.setData({
        residentTag: residentTag,
        certificateType: certificateType,
        residentType: residentType,
        genderDict: genderDict,
        selectedTags: [] // 初始化选中的标签
      });

      // 初始化带选中状态的标签列表
      this.updateResidentTagWithSelected();

      console.log('字典数据加载完成:', {
        residentTag: residentTag.length,
        certificateType: certificateType.length,
        residentType: residentType.length,
        genderDict: genderDict.length
      });
    } catch (error) {
      console.error('字典数据加载失败:', error);
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      });
    }
  },

  // 更新带选中状态的标签列表
  updateResidentTagWithSelected: function() {
    const residentTag = this.data.residentTag;
    const selectedTags = this.data.selectedTags;

    const residentTagWithSelected = residentTag.map(tag => ({
      ...tag,
      selected: selectedTags.some(selectedTag => selectedTag.nameEn === tag.nameEn)
    }));

    this.setData({
      residentTagWithSelected: residentTagWithSelected
    });
  },

  // ==================== 房屋选择弹窗相关方法 ====================

  // 显示房屋选择弹窗
  showHouseSelector: function() {
    this.resetHouseModalData();
    this.setData({
      showHouseSelector: true
    });
    this.loadBuildings();
  },

  // 隐藏房屋选择弹窗
  hideHouseSelector: function() {
    this.setData({
      showHouseSelector: false
    });
    this.resetHouseModalData();
  },

  // 重置弹窗数据
  resetHouseModalData: function() {
    this.setData({
      currentStep: 'building',
      searchKeyword: '',
      searchPlaceholder: '搜索楼栋',
      selectedBuildingId: '',
      selectedBuildingName: '',
      selectedUnitNumber: '',
      selectedRoomId: '',
      selectedRoomName: '',
      buildings: [],
      filteredBuildings: [],
      rooms: [],
      filteredRooms: []
    });
  },

  // 加载楼栋列表
  loadBuildings: function() {
    const communityId = wx.getStorageSync('selectedCommunity').id;
    if (!communityId) {
      wx.showToast({
        title: '请先选择小区',
        icon: 'none'
      });
      return;
    }

    const params = {
      pageNum: 1,
      pageSize: 500,
      communityId: communityId
    };

    houseApi.getBuildingsByCommunity(params)
      .then(res => {
        console.log('楼栋列表数据：', res);
        if (res && res.list) {
          this.setData({
            buildings: res.list,
            filteredBuildings: res.list
          });
        } else {
          wx.showToast({
            title: '获取楼栋列表失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('获取楼栋列表异常：', err);
        wx.showToast({
          title: '网络异常，请重试',
          icon: 'none'
        });
      });
  },

  // 加载房间列表
  loadRooms: function(buildingId) {
    if (!buildingId) {
      return;
    }

    houseApi.getRoomsByBuilding(buildingId)
      .then(res => {
        console.log('房间列表数据：', res);
        if (res && res.data && res.data.list) {
          this.setData({
            rooms: res.data.list,
            filteredRooms: res.data.list,
            currentStep: 'room',
            searchPlaceholder: '搜索房间'
          });
        } else {
          wx.showToast({
            title: '获取房间列表失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('获取房间列表异常：', err);
        wx.showToast({
          title: '网络异常，请重试',
          icon: 'none'
        });
      });
  },

  // 选择楼栋
  selectBuilding: function(e) {
    const buildingId = e.currentTarget.dataset.id;
    const buildingName = e.currentTarget.dataset.name;

    this.setData({
      selectedBuildingId: buildingId,
      selectedBuildingName: buildingName,
      searchKeyword: ''
    });

    this.loadRooms(buildingId);
  },

  // 选择房间
  selectRoom: function(e) {
    const roomId = e.currentTarget.dataset.id;
    const roomName = e.currentTarget.dataset.name;
    const unitNumber = e.currentTarget.dataset.unit;

    this.setData({
      selectedRoomId: roomId,
      selectedRoomName: roomName,
      selectedUnitNumber: unitNumber || ''
    });
  },

  // 确认选择房屋
  confirmHouseSelection: function() {
    if (!this.data.selectedRoomId) {
      wx.showToast({
        title: '请选择房间',
        icon: 'none'
      });
      return;
    }

    const formData = this.data.formData;
    const fullRoomName = `${this.data.selectedBuildingName}${this.data.selectedUnitNumber}${this.data.selectedRoomName}`;

    formData.roomId = this.data.selectedRoomId;
    formData.roomName = fullRoomName;

    // 清除该字段的错误信息
    const errors = this.data.errors;
    if (errors.roomId) {
      delete errors.roomId;
    }

    this.setData({
      formData: formData,
      errors: errors,
      showHouseSelector: false
    });
  },

  // 搜索输入
  onSearchInput: function(e) {
    const keyword = e.detail.value;
    this.setData({
      searchKeyword: keyword
    });

    if (this.data.currentStep === 'building') {
      this.filterBuildings(keyword);
    } else if (this.data.currentStep === 'room') {
      this.filterRooms(keyword);
    }
  },

  // 筛选楼栋
  filterBuildings: function(keyword) {
    const buildings = this.data.buildings;
    const filtered = keyword ?
      buildings.filter(building => building.buildingNumber.includes(keyword)) :
      buildings;

    this.setData({
      filteredBuildings: filtered
    });
  },

  // 筛选房间
  filterRooms: function(keyword) {
    const rooms = this.data.rooms;
    const filtered = keyword ?
      rooms.filter(room =>
        room.roomNumber.includes(keyword) ||
        (room.unitNumber && room.unitNumber.includes(keyword))
      ) :
      rooms;

    this.setData({
      filteredRooms: filtered
    });
  },

  // 返回楼栋选择
  backToBuilding: function() {
    this.setData({
      currentStep: 'building',
      searchKeyword: '',
      searchPlaceholder: '搜索楼栋',
      selectedBuildingId: '',
      selectedBuildingName: '',
      selectedUnitNumber: '',
      selectedRoomId: '',
      selectedRoomName: ''
    });
    this.filterBuildings('');
  },

  // ==================== 人员标签弹窗相关方法 ====================

  // 显示标签选择弹窗
  showTagSelector: function() {
    this.updateResidentTagWithSelected();
    this.setData({
      showTagSelector: true
    });
  },

  // 隐藏标签选择弹窗
  hideTagSelector: function() {
    this.setData({
      showTagSelector: false
    });
  },

  // 切换标签选择
  toggleTag: function(e) {
    const tagIndex = e.currentTarget.dataset.index;
    const tag = this.data.residentTag[tagIndex];
    const selectedTags = [...this.data.selectedTags];

    const existingIndex = selectedTags.findIndex(t => t.nameEn === tag.nameEn);

    if (existingIndex > -1) {
      // 取消选择
      selectedTags.splice(existingIndex, 1);
    } else {
      // 添加选择
      selectedTags.push(tag);
    }

    // 更新显示文本
    const selectedTagsText = selectedTags.map(tag => tag.nameCn).join('、');

    this.setData({
      selectedTags: selectedTags,
      selectedTagsText: selectedTagsText
    });

    // 更新带选中状态的标签列表
    this.updateResidentTagWithSelected();
  },

  // 确认标签选择
  confirmTagSelection: function() {
    const formData = this.data.formData;
    formData.tags = this.data.selectedTags.map(tag => tag.nameEn);

    // 更新显示文本
    const selectedTagsText = this.data.selectedTags.map(tag => tag.nameCn).join('、');

    this.setData({
      formData: formData,
      selectedTagsText: selectedTagsText,
      showTagSelector: false
    });
  },

  // 输入表单数据
  onInput: function(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    const formData = this.data.formData;

    // 更新对应字段
    formData[field] = value;

    // 清除该字段的错误信息
    const errors = this.data.errors;
    if (errors[field]) {
      delete errors[field];
    }

    this.setData({
      formData: formData,
      errors: errors
    });
  },

  // 选择性别
  onGenderChange: function(e) {
    const formData = this.data.formData;
    const index = e.detail.value;
    const genderDict = this.data.genderDict;

    if (index >= 0 && index < genderDict.length) {
      formData.gender = genderDict[index].nameEn;

      this.setData({
        formData: formData
      });
    }
  },

  // 选择出生日期
  onBirthDateChange: function(e) {
    const formData = this.data.formData;
    formData.birthday = e.detail.value;

    // 清除该字段的错误信息
    const errors = this.data.errors;
    if (errors.birthday) {
      delete errors.birthday;
    }

    this.setData({
      formData: formData,
      errors: errors
    });
  },

  // 选择证件类型
  onCertificateTypeChange: function(e) {
    const formData = this.data.formData;
    const index = e.detail.value;
    const certificateType = this.data.certificateType;

    if (index >= 0 && index < certificateType.length) {
      formData.certificateType = certificateType[index].nameEn;

      this.setData({
        formData: formData
      });
    }
  },

  // 选择居民类型
  onResidentTypeChange: function(e) {
    const formData = this.data.formData;
    const index = e.detail.value;
    const residentType = this.data.residentType;

    if (index >= 0 && index < residentType.length) {
      formData.residentType = residentType[index].nameEn;

      this.setData({
        formData: formData
      });
    }
  },





  // 验证表单
  validateForm: function() {
    const formData = this.data.formData;
    const errors = {};

    // 验证姓名
    if (!formData.residentName.trim()) {
      errors.residentName = '请输入姓名';
    }

    // 验证手机号
    if (!formData.phone.trim()) {
      errors.phone = '请输入手机号';
    } else if (!/^1\d{10}$/.test(formData.phone)) {
      errors.phone = '手机号格式不正确';
    }

    // 验证证件号码
    if (!formData.idCardNumber.trim()) {
      errors.idCardNumber = '请输入证件号码';
    } else if (formData.certificateType === 'id_card' && !/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(formData.idCardNumber)) {
      errors.idCardNumber = '身份证号格式不正确';
    }

    // 验证出生日期
    if (!formData.birthday) {
      errors.birthday = '请选择出生日期';
    }

    // 验证房屋
    if (!formData.roomId) {
      errors.roomId = '请选择关联房屋';
    }

    this.setData({
      errors: errors
    });

    return Object.keys(errors).length === 0;
  },

  // 提交表单
  submitForm: function() {
    // 验证表单
    if (!this.validateForm()) {
      wx.showToast({
        title: '请完善表单信息',
        icon: 'none'
      });
      return;
    }

    this.setData({
      submitting: true
    });

    const formData = this.data.formData;
    const communityId = wx.getStorageSync('selectedCommunity').id;

    // 构建提交参数
    const params = {
      certificateType: formData.certificateType,
      communityId: communityId,
      idCardNumber: formData.idCardNumber,
      note: formData.note || '',
      phone: formData.phone,
      residentName: formData.residentName,
      residentType: formData.residentType,
      roomId: formData.roomId,
      status: 'normal',
      tags: formData.tags.join(','),
      birthday: formData.birthday,
      gender: formData.gender
    };

    console.log('提交参数:', params);

    // 调用添加居民API
    propertyApi.addResident(params).then(res => {
      this.setData({
        submitting: false
      });

      if (res.code === 0) {
        wx.showToast({
          title: '添加成功',
          icon: 'success'
        });

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        wx.showToast({
          title: res.message || '添加失败',
          icon: 'none'
        });
      }
    }).catch(error => {
      console.error('添加居民失败:', error);
      this.setData({
        submitting: false
      });

      wx.showToast({
        title: '网络异常，请重试',
        icon: 'none'
      });
    });
  },

  // 取消添加
  cancelAdd: function() {
    wx.navigateBack();
  },

  // 阻止滑动穿透
  preventTouchMove: function() {
    return false;
  }
})